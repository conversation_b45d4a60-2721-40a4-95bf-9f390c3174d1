import concurrent
import glob
import importlib
import os
import sys
from concurrent.futures import as_completed

from loguru import logger as logging
from tqdm import tqdm


class SearchModule:
    def process_job(self, job_name, seen=True):
        job = importlib.import_module(f"company.{job_name}")
        try:
            getattr(job, "findIt")(seen)
            return None
        except Exception as e:
            if seen:
                logging.exception(f"Re<PERSON> failed for {job_name}")
            return (job_name, str(e))

    def search_jobs(self):
        companies = []
        failed_companies = []
        for file_path in glob.glob(os.path.abspath("company/*.py")):
            job_name = os.path.splitext(os.path.basename(file_path))[0]
            companies.append(job_name)
        time_out = 10 * 60
        with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
            futures = {executor.submit(self.process_job, company): company for company in companies}
            pbar = tqdm(total=len(companies), position=0, leave=True, file=sys.stderr)
            for future in as_completed(futures):
                company = futures[future]
                try:
                    result = future.result(timeout=time_out)
                    pbar.set_description(f"Processing: {company}")
                    if result:
                        failed_companies.append(company.split('.')[0])
                except concurrent.futures.TimeoutError:
                    logging.warning(f"Timeout processing company: {company}")
                    pbar.set_description(f"Timeout: {company}")
                    failed_companies.append(company)
                    try:
                        from run_all import kill_chrome
                        kill_chrome(force_kill_all=True)
                    except Exception as e:
                        pass
                pbar.update(1)
            pbar.close()
        logging.info(f"Failed companies: {failed_companies}")
        for failed_company in failed_companies:
            logging.info(f"Working on failed companies: {failed_company}")
            self.process_job(failed_company, seen=True)
