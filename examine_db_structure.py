#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to examine the current database structure for JobHunter application.
This will help us understand the existing schema before implementing duration tracking.
"""

import sys
import traceback
from module.databaseModule import databaseModule

def examine_database_structure():
    """Examine the current database structure and data."""
    try:
        db = databaseModule()
        
        print("=== JobHunter Database Structure Analysis ===\n")
        
        # Get all tables
        print("1. Available Tables:")
        tables_result = db.read("TABLE_NAME", table="INFORMATION_SCHEMA.TABLES", 
                               where="TABLE_SCHEMA = 'JobInfo'")
        tables = [table[0] for table in tables_result]
        for table in tables:
            print(f"   - {table}")
        
        print("\n" + "="*50 + "\n")
        
        # Examine key tables
        key_tables = ['job_info', 'application_status', 'user_info', 'user_balance', 'user_block']
        
        for table in key_tables:
            if table in tables:
                print(f"2. Table: {table}")
                print("-" * 30)
                
                # Get table structure
                try:
                    columns_result = db.read("COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT", 
                                           table="INFORMATION_SCHEMA.COLUMNS",
                                           where=f"TABLE_SCHEMA = 'JobInfo' AND TABLE_NAME = '{table}'")
                    
                    print("   Columns:")
                    for col in columns_result:
                        col_name, data_type, nullable, default = col
                        print(f"     - {col_name}: {data_type} (Nullable: {nullable}, Default: {default})")
                    
                    # Get sample data (first 3 rows)
                    print("\n   Sample Data (first 3 rows):")
                    sample_data = db.read(table=table, where="1=1 LIMIT 3")
                    if sample_data:
                        for i, row in enumerate(sample_data, 1):
                            print(f"     Row {i}: {row}")
                    else:
                        print("     No data found")
                        
                    # Get row count
                    count_result = db.read("COUNT(*)", table=table)
                    row_count = count_result[0][0] if count_result else 0
                    print(f"   Total rows: {row_count}")
                    
                except Exception as e:
                    print(f"   Error examining table {table}: {e}")
                
                print("\n" + "="*50 + "\n")
        
        # Check for existing duration-related fields
        print("3. Checking for existing duration/time tracking fields:")
        print("-" * 50)
        
        for table in tables:
            try:
                time_columns = db.read("COLUMN_NAME", 
                                     table="INFORMATION_SCHEMA.COLUMNS",
                                     where=f"TABLE_SCHEMA = 'JobInfo' AND TABLE_NAME = '{table}' AND "
                                           f"(COLUMN_NAME LIKE '%time%' OR COLUMN_NAME LIKE '%date%' OR "
                                           f"COLUMN_NAME LIKE '%duration%' OR COLUMN_NAME LIKE '%created%' OR "
                                           f"COLUMN_NAME LIKE '%updated%')")
                
                if time_columns:
                    print(f"   {table}:")
                    for col in time_columns:
                        print(f"     - {col[0]}")
            except Exception as e:
                print(f"   Error checking time fields in {table}: {e}")
        
        print("\n" + "="*50 + "\n")
        
        # Analyze application status data
        print("4. Application Status Analysis:")
        print("-" * 30)
        
        if 'application_status' in tables:
            try:
                # Get unique companies with application counts
                company_stats = db.read("company, COUNT(*) as app_count", 
                                      table="application_status", 
                                      where="1=1 GROUP BY company ORDER BY app_count DESC LIMIT 10")
                
                print("   Top 10 companies by application count:")
                for company, count in company_stats:
                    print(f"     - {company}: {count} applications")
                
                # Check if there are any date fields in application_status
                app_status_structure = db.read("COLUMN_NAME, DATA_TYPE", 
                                             table="INFORMATION_SCHEMA.COLUMNS",
                                             where="TABLE_SCHEMA = 'JobInfo' AND TABLE_NAME = 'application_status'")
                
                print("\n   Application Status Table Structure:")
                for col_name, data_type in app_status_structure:
                    print(f"     - {col_name}: {data_type}")
                    
            except Exception as e:
                print(f"   Error analyzing application status: {e}")
        
        print("\n" + "="*50 + "\n")
        
        # Analyze job_info data
        print("5. Job Info Analysis:")
        print("-" * 20)
        
        if 'job_info' in tables:
            try:
                # Check job_info structure
                job_info_structure = db.read("COLUMN_NAME, DATA_TYPE", 
                                           table="INFORMATION_SCHEMA.COLUMNS",
                                           where="TABLE_SCHEMA = 'JobInfo' AND TABLE_NAME = 'job_info'")
                
                print("   Job Info Table Structure:")
                for col_name, data_type in job_info_structure:
                    print(f"     - {col_name}: {data_type}")
                
                # Get recent job entries
                recent_jobs = db.read("company, title, retrievetime, applied", 
                                    table="job_info", 
                                    where="retrievetime >= DATE_SUB(NOW(), INTERVAL 7 DAY) LIMIT 5")
                
                print("\n   Recent Jobs (last 7 days, first 5):")
                for company, title, retrieve_time, applied in recent_jobs:
                    print(f"     - {company}: {title} (Retrieved: {retrieve_time}, Applied: {applied})")
                    
            except Exception as e:
                print(f"   Error analyzing job info: {e}")
        
        db.close_connection()
        
    except Exception as e:
        print(f"Error examining database: {e}")
        traceback.print_exc()

if __name__ == "__main__":
    examine_database_structure()
