"""
Job Search Duration Monitoring Module

This module provides functionality to track and monitor job search durations per company,
identifying which companies have taken the longest time during the job search process.
"""

import datetime
import json
import threading
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from loguru import logger

from module.databaseModule import databaseModule


@dataclass
class CompanyDuration:
    """Data class to represent company duration information."""
    company: str
    total_applications: int
    avg_duration_days: float
    max_duration_days: int
    min_duration_days: int
    longest_pending_job: Optional[str]
    longest_pending_days: int
    first_application_date: datetime.datetime
    last_application_date: datetime.datetime


class DurationTrackingModule:
    """
    Module for tracking and monitoring job search durations per company.
    
    This module calculates durations from initial job discovery (retrievetime) 
    to application submission, and tracks which companies have the longest 
    pending/in-progress durations.
    """
    
    def __init__(self):
        self.db = databaseModule()
        self._lock = threading.Lock()
        self._cache = {}
        self._cache_expiry = None
        self._cache_duration_minutes = 30  # Cache results for 30 minutes
        
    def _is_cache_valid(self) -> bool:
        """Check if the current cache is still valid."""
        if self._cache_expiry is None:
            return False
        return datetime.datetime.now() < self._cache_expiry
    
    def _update_cache(self, data: Dict):
        """Update the cache with new data."""
        self._cache = data
        self._cache_expiry = datetime.datetime.now() + datetime.timedelta(
            minutes=self._cache_duration_minutes
        )
    
    def calculate_company_durations(self, user_id: Optional[str] = None) -> List[CompanyDuration]:
        """
        Calculate duration statistics for all companies.
        
        Args:
            user_id: Optional user ID to filter results for specific user
            
        Returns:
            List of CompanyDuration objects sorted by longest pending duration
        """
        with self._lock:
            cache_key = f"durations_{user_id or 'all'}"
            
            if self._is_cache_valid() and cache_key in self._cache:
                logger.debug("Returning cached duration data")
                return self._cache[cache_key]
            
            try:
                # Build the query to get job and application data
                if user_id:
                    # Query for specific user
                    query = """
                        SELECT 
                            ji.company,
                            ji.jobid,
                            ji.title,
                            ji.retrievetime,
                            CASE WHEN aps.user_id IS NOT NULL THEN 1 ELSE 0 END as applied,
                            aps.user_id as applied_user,
                            DATEDIFF(COALESCE(
                                (SELECT MIN(created_at) FROM application_status 
                                 WHERE job_id = ji.jobid AND company = ji.company AND user_id = %s),
                                NOW()
                            ), ji.retrievetime) as duration_days
                        FROM job_info ji
                        LEFT JOIN application_status aps ON ji.jobid = aps.job_id 
                            AND ji.company = aps.company AND aps.user_id = %s
                        WHERE ji.retrievetime >= DATE_SUB(NOW(), INTERVAL 6 MONTH)
                        ORDER BY ji.company, ji.retrievetime
                    """
                    params = (user_id, user_id)
                else:
                    # Query for all users
                    query = """
                        SELECT 
                            ji.company,
                            ji.jobid,
                            ji.title,
                            ji.retrievetime,
                            CASE WHEN aps.user_id IS NOT NULL THEN 1 ELSE 0 END as applied,
                            aps.user_id as applied_user,
                            DATEDIFF(COALESCE(
                                (SELECT MIN(created_at) FROM application_status 
                                 WHERE job_id = ji.jobid AND company = ji.company),
                                NOW()
                            ), ji.retrievetime) as duration_days
                        FROM job_info ji
                        LEFT JOIN application_status aps ON ji.jobid = aps.job_id 
                            AND ji.company = aps.company
                        WHERE ji.retrievetime >= DATE_SUB(NOW(), INTERVAL 6 MONTH)
                        ORDER BY ji.company, ji.retrievetime
                    """
                    params = ()
                
                # Execute query using raw SQL since we need complex joins
                self.db.cursor.execute(query, params)
                results = self.db.cursor.fetchall()
                
                # Process results to calculate company statistics
                company_stats = {}
                
                for row in results:
                    company, job_id, title, retrieve_time, applied, applied_user, duration_days = row
                    
                    if company not in company_stats:
                        company_stats[company] = {
                            'jobs': [],
                            'applications': 0,
                            'durations': [],
                            'pending_jobs': [],
                            'first_seen': retrieve_time,
                            'last_seen': retrieve_time
                        }
                    
                    stats = company_stats[company]
                    stats['jobs'].append({
                        'job_id': job_id,
                        'title': title,
                        'retrieve_time': retrieve_time,
                        'applied': applied,
                        'duration_days': duration_days or 0
                    })
                    
                    # Update date ranges
                    if retrieve_time < stats['first_seen']:
                        stats['first_seen'] = retrieve_time
                    if retrieve_time > stats['last_seen']:
                        stats['last_seen'] = retrieve_time
                    
                    # Track applications and durations
                    if applied:
                        stats['applications'] += 1
                        stats['durations'].append(duration_days or 0)
                    else:
                        # This is a pending job
                        stats['pending_jobs'].append({
                            'job_id': job_id,
                            'title': title,
                            'duration_days': duration_days or 0
                        })
                
                # Convert to CompanyDuration objects
                duration_objects = []
                
                for company, stats in company_stats.items():
                    if not stats['jobs']:  # Skip companies with no jobs
                        continue
                    
                    # Calculate statistics
                    total_applications = stats['applications']
                    durations = stats['durations'] if stats['durations'] else [0]
                    avg_duration = sum(durations) / len(durations)
                    max_duration = max(durations) if durations else 0
                    min_duration = min(durations) if durations else 0
                    
                    # Find longest pending job
                    longest_pending_job = None
                    longest_pending_days = 0
                    
                    if stats['pending_jobs']:
                        longest_pending = max(stats['pending_jobs'], 
                                            key=lambda x: x['duration_days'])
                        longest_pending_job = longest_pending['title']
                        longest_pending_days = longest_pending['duration_days']
                    
                    duration_obj = CompanyDuration(
                        company=company,
                        total_applications=total_applications,
                        avg_duration_days=round(avg_duration, 1),
                        max_duration_days=max_duration,
                        min_duration_days=min_duration,
                        longest_pending_job=longest_pending_job,
                        longest_pending_days=longest_pending_days,
                        first_application_date=stats['first_seen'],
                        last_application_date=stats['last_seen']
                    )
                    
                    duration_objects.append(duration_obj)
                
                # Sort by longest pending duration (descending)
                duration_objects.sort(key=lambda x: x.longest_pending_days, reverse=True)
                
                # Update cache
                if not hasattr(self, '_cache'):
                    self._cache = {}
                self._cache[cache_key] = duration_objects
                self._update_cache(self._cache)
                
                logger.info(f"Calculated durations for {len(duration_objects)} companies")
                return duration_objects
                
            except Exception as e:
                logger.error(f"Error calculating company durations: {e}")
                return []
    
    def get_longest_duration_company(self, user_id: Optional[str] = None) -> Optional[CompanyDuration]:
        """
        Get the company with the longest pending duration.
        
        Args:
            user_id: Optional user ID to filter for specific user
            
        Returns:
            CompanyDuration object for the company with longest pending duration
        """
        durations = self.calculate_company_durations(user_id)
        return durations[0] if durations else None
    
    def get_top_longest_companies(self, limit: int = 10, user_id: Optional[str] = None) -> List[CompanyDuration]:
        """
        Get the top N companies with longest pending durations.
        
        Args:
            limit: Number of companies to return
            user_id: Optional user ID to filter for specific user
            
        Returns:
            List of CompanyDuration objects
        """
        durations = self.calculate_company_durations(user_id)
        return durations[:limit]
    
    def save_duration_report(self, filepath: str, user_id: Optional[str] = None) -> bool:
        """
        Save a duration report to a file.
        
        Args:
            filepath: Path to save the report
            user_id: Optional user ID to filter for specific user
            
        Returns:
            True if successful, False otherwise
        """
        try:
            durations = self.calculate_company_durations(user_id)
            
            report_data = {
                'generated_at': datetime.datetime.now().isoformat(),
                'user_id': user_id,
                'total_companies': len(durations),
                'companies': []
            }
            
            for duration in durations:
                company_data = {
                    'company': duration.company,
                    'total_applications': duration.total_applications,
                    'avg_duration_days': duration.avg_duration_days,
                    'max_duration_days': duration.max_duration_days,
                    'min_duration_days': duration.min_duration_days,
                    'longest_pending_job': duration.longest_pending_job,
                    'longest_pending_days': duration.longest_pending_days,
                    'first_application_date': duration.first_application_date.isoformat() if duration.first_application_date else None,
                    'last_application_date': duration.last_application_date.isoformat() if duration.last_application_date else None
                }
                report_data['companies'].append(company_data)
            
            with open(filepath, 'w') as f:
                json.dump(report_data, f, indent=2, default=str)
            
            logger.info(f"Duration report saved to {filepath}")
            return True
            
        except Exception as e:
            logger.error(f"Error saving duration report: {e}")
            return False
    
    def print_duration_summary(self, user_id: Optional[str] = None, limit: int = 10):
        """
        Print a summary of company durations to console.
        
        Args:
            user_id: Optional user ID to filter for specific user
            limit: Number of top companies to display
        """
        durations = self.get_top_longest_companies(limit, user_id)
        
        if not durations:
            print("No duration data available.")
            return
        
        print(f"\n{'='*80}")
        print(f"JOB SEARCH DURATION REPORT {'(User: ' + user_id + ')' if user_id else '(All Users)'}")
        print(f"Generated: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"{'='*80}")
        
        print(f"\nTOP {limit} COMPANIES BY LONGEST PENDING DURATION:")
        print(f"{'-'*80}")
        print(f"{'Company':<20} {'Apps':<5} {'Avg Days':<8} {'Max Days':<8} {'Pending Days':<12} {'Longest Pending Job':<25}")
        print(f"{'-'*80}")
        
        for duration in durations:
            pending_job = duration.longest_pending_job or "N/A"
            if len(pending_job) > 24:
                pending_job = pending_job[:21] + "..."
            
            print(f"{duration.company:<20} {duration.total_applications:<5} "
                  f"{duration.avg_duration_days:<8} {duration.max_duration_days:<8} "
                  f"{duration.longest_pending_days:<12} {pending_job:<25}")
        
        # Highlight the longest duration company
        if durations:
            longest = durations[0]
            print(f"\n🔥 LONGEST PENDING: {longest.company}")
            print(f"   Job: {longest.longest_pending_job or 'N/A'}")
            print(f"   Duration: {longest.longest_pending_days} days")
            print(f"   Total Applications: {longest.total_applications}")
    
    def close(self):
        """Close database connection."""
        if hasattr(self, 'db'):
            self.db.close_connection()
