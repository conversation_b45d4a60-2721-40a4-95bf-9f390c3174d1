"""
Job Search Duration Monitoring Module

This module tracks and monitors job search durations per company,
identifying which companies have taken the longest time during the job search process.
Uses simple text file storage for persistence and easy access.
"""

import datetime
import json
import os
import threading
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass, asdict
from loguru import logger

from module.databaseModule import databaseModule


@dataclass
class CompanyDuration:
    """Data class to represent company duration information."""
    company: str
    total_jobs_found: int
    total_applications: int
    pending_jobs_count: int
    avg_duration_days: float
    max_duration_days: int
    longest_pending_job: Optional[str]
    longest_pending_days: int
    first_job_date: str
    last_job_date: str


class DurationTrackingModule:
    """
    Module for tracking and monitoring job search durations per company.

    Calculates durations from initial job discovery (retrievetime) to current status,
    and tracks which companies have the longest pending/in-progress durations.
    Uses simple text files for storage and persistence.
    """

    def __init__(self, data_dir: str = "duration_tracking"):
        self.db = databaseModule()
        self.data_dir = data_dir
        self._lock = threading.Lock()

        # Create data directory if it doesn't exist
        os.makedirs(self.data_dir, exist_ok=True)

        # File paths for different reports
        self.duration_report_file = os.path.join(self.data_dir, "company_durations.json")
        self.longest_company_file = os.path.join(self.data_dir, "longest_duration_company.txt")
        self.summary_report_file = os.path.join(self.data_dir, "duration_summary.txt")
        self.daily_log_file = os.path.join(self.data_dir, f"duration_log_{datetime.datetime.now().strftime('%Y%m')}.txt")

    def calculate_company_durations(self, user_id: Optional[str] = None) -> List[CompanyDuration]:
        """
        Calculate duration statistics for all companies by analyzing existing database data.

        Args:
            user_id: Optional user ID to filter results for specific user

        Returns:
            List of CompanyDuration objects sorted by longest pending duration
        """
        with self._lock:
            try:
                logger.info("Calculating company durations from database...")

                # Get all jobs from job_info table (last 6 months for relevance)
                jobs_data = self.db.read(
                    columns="company, jobid, title, retrievetime, applied",
                    table="job_info",
                    where="retrievetime >= DATE_SUB(NOW(), INTERVAL 6 MONTH)"
                )

                # Get all applications from application_status table
                if user_id:
                    applications_data = self.db.read(
                        columns="user_id, job_id, company",
                        table="application_status",
                        where=f"user_id = '{user_id}'"
                    )
                else:
                    applications_data = self.db.read(
                        columns="user_id, job_id, company",
                        table="application_status"
                    )

                # Create a set of applied jobs for quick lookup
                applied_jobs = set()
                for app in applications_data:
                    user, job_id, company = app
                    applied_jobs.add((job_id, company.lower()))

                # Process jobs data to calculate company statistics
                company_stats = {}
                current_date = datetime.datetime.now()

                for job in jobs_data:
                    company, job_id, title, retrieve_time, applied_flag = job
                    company_lower = company.lower()

                    # Handle different datetime formats
                    if isinstance(retrieve_time, str):
                        try:
                            retrieve_time = datetime.datetime.fromisoformat(retrieve_time.replace('Z', '+00:00'))
                        except:
                            try:
                                retrieve_time = datetime.datetime.strptime(retrieve_time, '%Y-%m-%d %H:%M:%S')
                            except:
                                logger.warning(f"Could not parse date: {retrieve_time}")
                                continue

                    # Calculate duration from retrieve time to now
                    duration_days = (current_date - retrieve_time).days

                    # Check if this job was actually applied to
                    is_applied = (job_id, company_lower) in applied_jobs or applied_flag == '1'

                    if company not in company_stats:
                        company_stats[company] = {
                            'jobs': [],
                            'applications': 0,
                            'pending_jobs': [],
                            'first_seen': retrieve_time,
                            'last_seen': retrieve_time
                        }

                    stats = company_stats[company]
                    stats['jobs'].append({
                        'job_id': job_id,
                        'title': title,
                        'retrieve_time': retrieve_time,
                        'applied': is_applied,
                        'duration_days': duration_days
                    })

                    # Update date ranges
                    if retrieve_time < stats['first_seen']:
                        stats['first_seen'] = retrieve_time
                    if retrieve_time > stats['last_seen']:
                        stats['last_seen'] = retrieve_time

                    # Track applications and pending jobs
                    if is_applied:
                        stats['applications'] += 1
                    else:
                        # This is a pending job
                        stats['pending_jobs'].append({
                            'job_id': job_id,
                            'title': title,
                            'duration_days': duration_days
                        })

                # Convert to CompanyDuration objects
                duration_objects = []

                for company, stats in company_stats.items():
                    if not stats['jobs']:  # Skip companies with no jobs
                        continue

                    # Calculate statistics
                    total_jobs = len(stats['jobs'])
                    total_applications = stats['applications']
                    pending_count = len(stats['pending_jobs'])

                    # Calculate average duration for all jobs (applied and pending)
                    all_durations = [job['duration_days'] for job in stats['jobs']]
                    avg_duration = sum(all_durations) / len(all_durations) if all_durations else 0
                    max_duration = max(all_durations) if all_durations else 0

                    # Find longest pending job
                    longest_pending_job = None
                    longest_pending_days = 0

                    if stats['pending_jobs']:
                        longest_pending = max(stats['pending_jobs'],
                                            key=lambda x: x['duration_days'])
                        longest_pending_job = longest_pending['title']
                        longest_pending_days = longest_pending['duration_days']

                    duration_obj = CompanyDuration(
                        company=company,
                        total_jobs_found=total_jobs,
                        total_applications=total_applications,
                        pending_jobs_count=pending_count,
                        avg_duration_days=round(avg_duration, 1),
                        max_duration_days=max_duration,
                        longest_pending_job=longest_pending_job,
                        longest_pending_days=longest_pending_days,
                        first_job_date=stats['first_seen'].strftime('%Y-%m-%d'),
                        last_job_date=stats['last_seen'].strftime('%Y-%m-%d')
                    )

                    duration_objects.append(duration_obj)

                # Sort by longest pending duration (descending)
                duration_objects.sort(key=lambda x: x.longest_pending_days, reverse=True)

                logger.info(f"Calculated durations for {len(duration_objects)} companies")
                return duration_objects

            except Exception as e:
                logger.error(f"Error calculating company durations: {e}")
                return []

    def save_duration_data(self, user_id: Optional[str] = None) -> bool:
        """
        Calculate and save duration data to text files.

        Args:
            user_id: Optional user ID to filter for specific user

        Returns:
            True if successful, False otherwise
        """
        try:
            durations = self.calculate_company_durations(user_id)

            if not durations:
                logger.warning("No duration data to save")
                return False

            # Save detailed JSON report
            report_data = {
                'generated_at': datetime.datetime.now().isoformat(),
                'user_id': user_id,
                'total_companies': len(durations),
                'companies': [asdict(duration) for duration in durations]
            }

            with open(self.duration_report_file, 'w') as f:
                json.dump(report_data, f, indent=2, default=str)

            # Save longest duration company info (simple text format)
            longest = durations[0]
            with open(self.longest_company_file, 'w') as f:
                f.write(f"LONGEST DURATION COMPANY REPORT\n")
                f.write(f"Generated: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"User: {user_id or 'All Users'}\n")
                f.write(f"{'='*50}\n\n")
                f.write(f"🔥 COMPANY: {longest.company}\n")
                f.write(f"📋 Longest Pending Job: {longest.longest_pending_job or 'N/A'}\n")
                f.write(f"⏰ Longest Pending Duration: {longest.longest_pending_days} days\n")
                f.write(f"📊 Total Jobs Found: {longest.total_jobs_found}\n")
                f.write(f"✅ Total Applications: {longest.total_applications}\n")
                f.write(f"⏳ Pending Jobs: {longest.pending_jobs_count}\n")
                f.write(f"📈 Average Duration: {longest.avg_duration_days} days\n")
                f.write(f"📅 First Job: {longest.first_job_date}\n")
                f.write(f"📅 Last Job: {longest.last_job_date}\n")

            # Save summary report
            self._save_summary_report(durations, user_id)

            # Log to daily log file
            self._log_to_daily_file(longest, user_id)

            logger.info(f"Duration data saved to {self.data_dir}")
            return True

        except Exception as e:
            logger.error(f"Error saving duration data: {e}")
            return False

    def _save_summary_report(self, durations: List[CompanyDuration], user_id: Optional[str]):
        """Save a human-readable summary report."""
        with open(self.summary_report_file, 'w') as f:
            f.write(f"JOB SEARCH DURATION SUMMARY REPORT\n")
            f.write(f"Generated: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"User: {user_id or 'All Users'}\n")
            f.write(f"{'='*80}\n\n")

            if not durations:
                f.write("No duration data available.\n")
                return

            f.write(f"TOP 20 COMPANIES BY LONGEST PENDING DURATION:\n")
            f.write(f"{'-'*80}\n")
            f.write(f"{'Rank':<4} {'Company':<20} {'Jobs':<5} {'Apps':<5} {'Pending':<8} {'Avg Days':<8} {'Max Days':<8} {'Longest Job':<20}\n")
            f.write(f"{'-'*80}\n")

            for i, duration in enumerate(durations[:20], 1):
                pending_job = duration.longest_pending_job or "N/A"
                if len(pending_job) > 19:
                    pending_job = pending_job[:16] + "..."

                f.write(f"{i:<4} {duration.company:<20} {duration.total_jobs_found:<5} "
                       f"{duration.total_applications:<5} {duration.pending_jobs_count:<8} "
                       f"{duration.avg_duration_days:<8} {duration.max_duration_days:<8} {pending_job:<20}\n")

            # Add statistics
            f.write(f"\n{'='*80}\n")
            f.write(f"STATISTICS:\n")
            f.write(f"Total Companies Analyzed: {len(durations)}\n")
            f.write(f"Companies with Pending Jobs: {sum(1 for d in durations if d.pending_jobs_count > 0)}\n")
            f.write(f"Total Jobs Found: {sum(d.total_jobs_found for d in durations)}\n")
            f.write(f"Total Applications: {sum(d.total_applications for d in durations)}\n")

            if durations:
                longest = durations[0]
                f.write(f"\n🔥 LONGEST PENDING COMPANY:\n")
                f.write(f"Company: {longest.company}\n")
                f.write(f"Duration: {longest.longest_pending_days} days\n")
                f.write(f"Job: {longest.longest_pending_job or 'N/A'}\n")
                f.write(f"Total Applications: {longest.total_applications}\n")
                f.write(f"Pending Jobs: {longest.pending_jobs_count}\n")

    def _log_to_daily_file(self, longest: CompanyDuration, user_id: Optional[str]):
        """Log the longest duration company to a daily log file."""
        timestamp = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        log_entry = (f"[{timestamp}] LONGEST: {longest.company} - {longest.longest_pending_days} days "
                    f"({longest.longest_pending_job or 'N/A'}) - Apps: {longest.total_applications}, "
                    f"Pending: {longest.pending_jobs_count}\n")

        with open(self.daily_log_file, 'a') as f:
            f.write(log_entry)

    def get_longest_duration_company(self, user_id: Optional[str] = None) -> Optional[CompanyDuration]:
        """Get the company with the longest pending duration."""
        durations = self.calculate_company_durations(user_id)
        return durations[0] if durations else None

    def print_duration_summary(self, user_id: Optional[str] = None, limit: int = 10):
        """Print a summary of company durations to console."""
        durations = self.calculate_company_durations(user_id)[:limit]

        if not durations:
            print("No duration data available.")
            return

        print(f"\n{'='*80}")
        print(f"JOB SEARCH DURATION REPORT {'(User: ' + user_id + ')' if user_id else '(All Users)'}")
        print(f"Generated: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"{'='*80}")

        print(f"\nTOP {limit} COMPANIES BY LONGEST PENDING DURATION:")
        print(f"{'-'*80}")
        print(f"{'Company':<20} {'Jobs':<5} {'Apps':<5} {'Pending':<8} {'Avg Days':<8} {'Max Days':<8} {'Longest Job':<20}")
        print(f"{'-'*80}")

        for duration in durations:
            pending_job = duration.longest_pending_job or "N/A"
            if len(pending_job) > 19:
                pending_job = pending_job[:16] + "..."

            print(f"{duration.company:<20} {duration.total_jobs_found:<5} "
                  f"{duration.total_applications:<5} {duration.pending_jobs_count:<8} "
                  f"{duration.avg_duration_days:<8} {duration.max_duration_days:<8} {pending_job:<20}")

        # Highlight the longest duration company
        if durations:
            longest = durations[0]
            print(f"\n🔥 LONGEST PENDING: {longest.company}")
            print(f"   Job: {longest.longest_pending_job or 'N/A'}")
            print(f"   Duration: {longest.longest_pending_days} days")
            print(f"   Total Applications: {longest.total_applications}")
            print(f"   Pending Jobs: {longest.pending_jobs_count}")

    def get_report_file_paths(self) -> Dict[str, str]:
        """Get the file paths for all generated reports."""
        return {
            'detailed_json': self.duration_report_file,
            'longest_company': self.longest_company_file,
            'summary_report': self.summary_report_file,
            'daily_log': self.daily_log_file
        }

    def close(self):
        """Close database connection."""
        if hasattr(self, 'db'):
            self.db.close_connection()