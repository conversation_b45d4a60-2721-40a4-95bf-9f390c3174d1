from abc import ABC

from selenium.webdriver.common.by import By

from module.applicationInterfaceModule import applicationInterface as applyInterface


class snowflakeApply(applyInterface, ABC):

    def applyIt(self, url, wd):
        wd.get(url)
        try:
            self.expected_shown_element(wd, '[class="btn primary-button au-target"]')
            wd.get(wd.find_element(By.CSS_SELECTOR, '[class="job-header-actions"]')
                   .find_element(By.CSS_SELECTOR, 'a').get_attribute('href'))
        except:
            if wd.find_element(By.CSS_SELECTOR, '[data-ph-at-id="job-completion-info"]').text.startswith("OH SNAP"):
                self.database.delete(f"link='{url}'")
                return -4
        try:
            self.expected_shown_element(wd, '[id="job-application-form"]')
        except:
            self.expected_shown_element(wd, 'h1')
            next(filter(lambda x: x.text.startswith("Page not found"), wd.find_elements(By.CSS_SELECTOR, 'h1')))
            self.database.delete(f"link='{url}'")
            return -4
        self.click_ele(wd, '[id="job-application-form"]')
        self.expected_shown_element(wd, '[id="_systemfield_name"]')
        nav_class = wd.find_element(By.XPATH, '//*[@id="root"]/div[1]/ul').get_attribute("class")
        self.hide.hideElement(wd, f'[class="{nav_class}"]')
        wd.find_element(By.CSS_SELECTOR, '[id="_systemfield_resume"]').find_element(By.XPATH, '..').find_element(
            By.CSS_SELECTOR, 'button').click()
        wd.find_element(By.CSS_SELECTOR, 'input[type="file"]').send_keys(self.user_resume_path)
        self.wait_x_sec(15)
        name = wd.find_element(By.CSS_SELECTOR, '[id="_systemfield_name"]')
        name.clear()
        name.send_keys(self.user_name)
        email = wd.find_element(By.CSS_SELECTOR, '[id="_systemfield_email"]')
        email.clear()
        email.send_keys(self.user_account)
        for question_label in list(
                filter(lambda x: "required" in x.get_attribute("class"), wd.find_elements(By.XPATH,
                                                                                          "//label[contains(@class, 'ashby-application-form-question-title')]"))):
            question = question_label.find_element(By.XPATH, '..')
            question_text = question_label.text
            if question_text.startswith("Phone"):
                try:
                    phone = question.find_element(By.CSS_SELECTOR, 'input[name="phone"]')
                except:
                    phone = question.find_element(By.CSS_SELECTOR, 'input[type="tel"]')
                phone.send_keys(self.user_phone)
            elif question_text.startswith("Location"):
                question.find_element(By.CSS_SELECTOR, 'input[role="combobox"]').send_keys(
                    ", ".join([self.user_city, self.user_state]))
                self.expected_shown_element(wd, '[role="option"]')
                wd.find_elements(By.CSS_SELECTOR, '[role="option"]')[0].click()
            elif question_text.startswith(
                    "Snowflake will process your personal information") or question_text.startswith(
                    "In any materials you submit"):
                question.find_element(By.CSS_SELECTOR, 'input[type="checkbox"]').click()
            elif question_text.startswith("Have you worked at Snowflake") or question_text.startswith(
                    "[US Applicants Only] Are you subject to any post-employment restrictions") or question_text.startswith(
                "Are you subject to any post-employment restrictions"):
                ans = next(filter(lambda x: x.text.title().startswith('No'),
                                  question.find_elements(By.CSS_SELECTOR, 'button')))
                self.click_ele(wd, ans)
            elif question_text.startswith("For Snowflake to anticipate possible"):
                ans = next(filter(lambda x: x.text.title().startswith('Yes'),
                                  question.find_elements(By.CSS_SELECTOR, 'button')))
                self.click_ele(wd, ans)
            elif question_text.startswith("Will you require sponsorship"):
                ans = next(filter(lambda x: x.text.title().startswith(self.user_visa_ans),
                                  question.find_elements(By.CSS_SELECTOR, 'button')))
                self.click_ele(wd, ans)
            elif question_text.startswith("Where have you most recently worked"):
                question.find_element(By.CSS_SELECTOR, 'input[type="text"]').send_keys(self.user_company)
            elif question_text.startswith(
                    "A “U.S. person” is a citizen, legal permanent resident") or question_text.startswith(
                "Due to SEC auditor independence requirements"):
                ans_id = next(filter(
                    lambda x: x.find_element(By.XPATH, '../..').find_element(By.CSS_SELECTOR,
                                                                             'label').text.title().startswith("No"),
                    question.find_elements(By.CSS_SELECTOR, 'input[type="radio"]'))).get_attribute("id")
                self.click_ele(wd, f'[id="{ans_id}"]')
            elif question_text.startswith("Full Name"):
                input_a = question.find_element(By.CSS_SELECTOR, 'input[type="text"]')
                if input_a.get_attribute("name") != "_systemfield_name":
                    input_a.send_keys(self.user_name)
            else:
                if question_text not in ["Name", "Email", "Resume", "Location"]:
                    self.logger.error(f"Company: {self.company.title()}, Question: {question_text}, {url}, {self.user}")
        submit_btn = wd.find_element(By.XPATH, "//span[contains(text(), 'Submit Application')]")
        self.click_ele(wd, submit_btn)
        try:
            self.expected_shown_element(wd, '[data-highlight="positive"]')
            return 1
        except:
            return 0
