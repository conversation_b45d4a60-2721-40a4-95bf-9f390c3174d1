#!/usr/bin/env python3
"""
Test Script for JobHunter Solutions

This script tests both the duration tracking and Chrome driver management solutions.
"""

import sys
import os
import time
from loguru import logger

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))


def test_duration_tracking():
    """Test the duration tracking functionality."""
    logger.info("Testing Duration Tracking Module...")
    
    try:
        from module.durationTrackingModule import DurationTrackingModule
        
        # Initialize tracker
        tracker = DurationTrackingModule(data_dir="test_duration_tracking")
        
        # Test calculation
        logger.info("Calculating company durations...")
        durations = tracker.calculate_company_durations()
        
        if durations:
            logger.info(f"Found {len(durations)} companies with duration data")
            
            # Test getting longest duration company
            longest = tracker.get_longest_duration_company()
            if longest:
                logger.info(f"Longest pending company: {longest.company} ({longest.longest_pending_days} days)")
            
            # Test saving data
            logger.info("Testing data saving...")
            success = tracker.save_duration_data()
            if success:
                logger.info("✅ Duration data saved successfully")
                file_paths = tracker.get_report_file_paths()
                for report_type, path in file_paths.items():
                    if os.path.exists(path):
                        logger.info(f"   ✅ {report_type}: {path}")
                    else:
                        logger.warning(f"   ❌ {report_type}: {path} not found")
            else:
                logger.warning("❌ Failed to save duration data")
            
            # Test console output
            logger.info("Testing console output...")
            tracker.print_duration_summary(limit=5)
            
        else:
            logger.warning("No duration data found - this might be expected if database is empty")
        
        # Close tracker
        tracker.close()
        logger.info("✅ Duration tracking test completed")
        return True
        
    except Exception as e:
        logger.error(f"❌ Duration tracking test failed: {e}")
        return False


def test_chrome_driver_management():
    """Test the Chrome driver management improvements."""
    logger.info("Testing Chrome Driver Management...")
    
    try:
        from module.webdriverModule import WebdriverModule
        
        # Test driver status
        logger.info("Checking chromedriver status...")
        status = WebdriverModule.get_chromedriver_status()
        logger.info(f"Driver status: {status}")
        
        # Test persistence flag
        logger.info("Testing driver persistence...")
        is_persistent = WebdriverModule.is_driver_persistent()
        logger.info(f"Driver persistent: {is_persistent}")
        
        # Test creating a WebDriver instance
        logger.info("Testing WebDriver instance creation...")
        wd_module = WebdriverModule()
        
        # Test waiting for driver ready
        logger.info("Testing driver ready wait...")
        ready = WebdriverModule.wait_for_driver_ready(timeout_seconds=10)
        logger.info(f"Driver ready: {ready}")
        
        if ready:
            logger.info("✅ Chrome driver management test completed successfully")
            return True
        else:
            logger.warning("⚠️ Chrome driver not ready within timeout")
            return False
        
    except Exception as e:
        logger.error(f"❌ Chrome driver management test failed: {e}")
        return False


def test_improved_reset_logic():
    """Test the improved reset logic."""
    logger.info("Testing improved reset logic...")
    
    try:
        from module.webdriverModule import WebdriverModule
        
        # Test soft reset
        logger.info("Testing soft reset...")
        WebdriverModule.reset_chromedriver_check(force_redownload=False)
        
        # Test force reset
        logger.info("Testing force reset...")
        WebdriverModule.reset_chromedriver_check(force_redownload=True)
        
        logger.info("✅ Reset logic test completed")
        return True
        
    except Exception as e:
        logger.error(f"❌ Reset logic test failed: {e}")
        return False


def main():
    """Run all tests."""
    logger.info("🚀 Starting JobHunter Solutions Test Suite")
    logger.info("="*60)
    
    results = []
    
    # Test 1: Duration Tracking
    logger.info("\n📊 TEST 1: Duration Tracking")
    logger.info("-" * 40)
    results.append(("Duration Tracking", test_duration_tracking()))
    
    # Test 2: Chrome Driver Management
    logger.info("\n🌐 TEST 2: Chrome Driver Management")
    logger.info("-" * 40)
    results.append(("Chrome Driver Management", test_chrome_driver_management()))
    
    # Test 3: Improved Reset Logic
    logger.info("\n🔄 TEST 3: Improved Reset Logic")
    logger.info("-" * 40)
    results.append(("Reset Logic", test_improved_reset_logic()))
    
    # Summary
    logger.info("\n📋 TEST RESULTS SUMMARY")
    logger.info("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        logger.info(f"{test_name:<30} {status}")
        if result:
            passed += 1
    
    logger.info("-" * 60)
    logger.info(f"Tests Passed: {passed}/{total}")
    
    if passed == total:
        logger.info("🎉 All tests passed! Both solutions are working correctly.")
        return 0
    else:
        logger.warning(f"⚠️ {total - passed} test(s) failed. Please check the logs above.")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
