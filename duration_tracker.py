#!/usr/bin/env python3
"""
Job Search Duration Tracker

This script provides a simple interface to track and monitor job search durations
per company, identifying which companies have taken the longest time during the
job search process.

Usage:
    python duration_tracker.py [--user USER_ID] [--save] [--print] [--limit N]
"""

import argparse
import sys
import os

# Add the current directory to Python path so we can import modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from module.durationTrackingModule import DurationTrackingModule
from loguru import logger


def main():
    parser = argparse.ArgumentParser(description='Track job search durations per company')
    parser.add_argument('--user', type=str, help='Filter results for specific user ID')
    parser.add_argument('--save', action='store_true', help='Save duration data to files')
    parser.add_argument('--print', action='store_true', default=True, help='Print duration summary to console')
    parser.add_argument('--limit', type=int, default=10, help='Number of top companies to display (default: 10)')
    parser.add_argument('--data-dir', type=str, default='duration_tracking', help='Directory to save data files')
    
    args = parser.parse_args()
    
    try:
        # Initialize the duration tracking module
        logger.info("Initializing Duration Tracking Module...")
        tracker = DurationTrackingModule(data_dir=args.data_dir)
        
        # Print summary to console
        if args.print:
            logger.info("Generating duration summary...")
            tracker.print_duration_summary(user_id=args.user, limit=args.limit)
        
        # Save data to files if requested
        if args.save:
            logger.info("Saving duration data to files...")
            success = tracker.save_duration_data(user_id=args.user)
            if success:
                file_paths = tracker.get_report_file_paths()
                print(f"\n📁 Duration data saved to:")
                for report_type, file_path in file_paths.items():
                    print(f"   {report_type}: {file_path}")
            else:
                print("❌ Failed to save duration data")
        
        # Get and display the longest duration company
        longest_company = tracker.get_longest_duration_company(user_id=args.user)
        if longest_company:
            print(f"\n🎯 QUICK SUMMARY:")
            print(f"   Longest Pending Company: {longest_company.company}")
            print(f"   Duration: {longest_company.longest_pending_days} days")
            print(f"   Job: {longest_company.longest_pending_job or 'N/A'}")
            print(f"   Total Applications: {longest_company.total_applications}")
            print(f"   Pending Jobs: {longest_company.pending_jobs_count}")
        
        # Close database connection
        tracker.close()
        logger.info("Duration tracking completed successfully")
        
    except Exception as e:
        logger.error(f"Error running duration tracker: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
