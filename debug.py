import glob
import importlib
import json
import os

import loguru

from apply import ApplyModule

logger = loguru.logger
ERROR_MSG = """

https://jobs.careers.microsoft.com/global/en/job/1857043/Senior-Software-Engineer, 5854654116

"""
LINK, DEBUG_USER = ERROR_MSG.replace(" ", "").replace("\n", "").split(",")
DEBUG_COMPANY = LINK.split(".")[1]
if DEBUG_COMPANY == 'taleo' or DEBUG_COMPANY.startswith('wd') or DEBUG_COMPANY.startswith(
        'fa') or DEBUG_COMPANY.startswith('careers') or DEBUG_COMPANY.startswith('com'):
    DEBUG_COMPANY = LINK.split(".")[0].replace("https://", "")
if 'explore' in LINK:
    DEBUG_COMPANY = 'netflix'
if DEBUG_COMPANY == 'ashbyhq':
    DEBUG_COMPANY = LINK.split("/")[3]
if DEBUG_COMPANY == "myworkdaysite":
    DEBUG_COMPANY = LINK.split("/")[5]
if DEBUG_COMPANY == "greenhouse":
    DEBUG_COMPANY = LINK.split("/")[3]
if DEBUG_COMPANY == 'careerpuck':
    DEBUG_COMPANY = LINK.split("/")[-3]
if DEBUG_COMPANY == 'eightfold':
    DEBUG_COMPANY = LINK.split("/")[2].split(".")[0]
if DEBUG_COMPANY == 'embed':
    DEBUG_COMPANY = LINK.split("for=")[-1].split("&")[0]
try:
    company_name_map = {
        "eeho": "oracle",
        "cvshealth": "cvs",
        "axp": "amex",
        "expediagroup": "expedia",
        "jpmc": "chase",
        "schwabjobs": "schwab",
        "ebayinc": "ebay",
        "thetradedesk": "tradedesk",
        "tbe": "costco",
        "pinterestcareers": "pinterest",
        "ouryahoo": "yahoo",
        "fmr": "fidelity",
        "metacareers": "meta",
        "hcmportal": "ups",
        "snapchat": "snap",
        "cat": "caterpillar",
        "datadoghq": "datadog",
        "oreillyauto": "oreillyautoparts",
        "wf": "wellsfargo",
        "ghr": "bankofamerica",
        "globalhr": "RTX",
        "jobs": "microsoft",
        "weareroku": "roku",
        "sec": "samsung",
        "doordashusa": "doordash",
        "avature": "bloomberg",
        "onepeloton": "peloton",
        "lifeatspotify": 'spotify',
        "withwaymo": "waymo",
    }
    DEBUG_COMPANY = company_name_map[DEBUG_COMPANY]
except:
    pass
companies_apply_dict = {}
for file_path in glob.glob(os.path.abspath("application/*.py")):
    company_name = os.path.splitext(os.path.basename(file_path))[0]
    job = importlib.import_module(f"application.{company_name}")
    companies_apply_dict[company_name] = getattr(job, f"{company_name}Apply")
user_items = json.load(open('user_dict.json', 'r'))
try:
    ApplyModule(DEBUG_USER, user_items, companies_apply_dict).debug_job_link(DEBUG_COMPANY, DEBUG_USER, LINK)
except Exception as e:
    logger.exception(e)
