import datetime
import re

from selenium.webdriver.common.by import By

from module.companyModule import companyModule as Base


def findIt(seen):
    search = Base("anthropic")
    experience = search.experience
    db = search.db
    geo = search.geo
    company_name = search.name
    url = "https://job-boards.greenhouse.io/anthropic?offices%5B%5D=4039647008&offices%5B%5D=4011066008&offices%5B%5D=4001219008&offices%5B%5D=4008537008&offices%5B%5D=4001218008&offices%5B%5D=4011228008&offices%5B%5D=4001217008"
    wd = search.wd.driver_chrome_hide() if not seen else search.wd.driver_chrome_seen()
    wd.get(url)
    try:
        target = int(wd.find_element(By.CSS_SELECTOR, '[aria-label="Pagination"]')
                     .find_element(By.CSS_SELECTOR, 'ul').find_elements(By.CSS_SELECTOR, 'li')[-1].text)
    except:
        target = 1
    for _ in range(target):
        job_list = wd.find_elements(By.CSS_SELECTOR, '[class="job-post"]')
        for job in job_list:
            job_title, job_loc = job.find_element(By.CSS_SELECTOR, 'a').text.split("\n")
            job_link = job.find_element(By.CSS_SELECTOR, 'a').get_attribute('href')
            job_id = job_link.split('/')[-1].split("?")[0]
            if "|" in job_loc:
                job_loc = job_loc.split("|")[0]
            if bool(re.search(geo.getGeoUSRe(), job_loc)) and experience.whatIWant(job_title) and len(
                    db.read(columns="jobid", where=f"jobid = '{job_id}' and company = '{company_name}'")) == 0:
                job_state = search.get_job_location_by_job_link_location(job_loc)
                if not job_state:
                    job_state = search.get_job_location_by_requests(job_loc)
                job_loc = job_state
                db.create({"company": f"{company_name}", "jobid": f"{job_id}", "title": f"{job_title}",
                           "retrievetime": f"{datetime.datetime.now()}", "applied": "0", "link": f"{job_link}",
                           "location": f"{job_loc}"})
        try:
            search.click_ele(wd, '[aria-label="Next page"]')
            search.wait_x_sec(3)
        except:
            pass
