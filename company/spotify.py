import datetime

from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.ui import WebDriverWait

from module.companyModule import companyModule as Base


def findIt(seen: bool):
    url = f"https://www.lifeatspotify.com/jobs?l=boston&l=los-angeles&l=new-york&l=united-states-of-america-home-mix"
    search = Base('spotify')
    wd = search.wd.driver_chrome_hide() if not seen else search.wd.driver_chrome_seen()
    wd.get(url)
    experience = search.experience
    db = search.db
    company_name = search.name
    WebDriverWait(wd, 10).until(EC.presence_of_element_located((By.CSS_SELECTOR, '[aria-label="Load more jobs"]')))
    flag = len(wd.find_elements(By.CSS_SELECTOR, '[aria-label="Load more jobs"]')) > 0
    search.hide.hideElement(wd, '[id="main-menu"]')
    while flag:
        search.click_ele(wd, '[aria-label="Load more jobs"]')
        flag = len(wd.find_elements(By.CSS_SELECTOR, '[aria-label="Load more jobs"]')) > 0
    jobs = wd.find_element(By.CSS_SELECTOR, '[id="search-view"]').find_elements(By.CSS_SELECTOR, '[data-item="true"]')
    for job in jobs:
        job_title = job.get_attribute("data-info")
        job_link = f"https://www.lifeatspotify.com/jobs/{job_title}"
        job_id = hash(job_title)
        job_loc = job.find_elements(By.CSS_SELECTOR, 'p')[1].text
        job_title = job_title.replace("-", " ").title()
        if experience.whatIWant(job_title) and len(
                db.read(columns="jobid", where=f"jobid = '{job_id}' and company = '{company_name}'")) == 0:
            job_state = search.get_job_location_by_job_link_location(job_loc)
            if not job_state:
                job_state = search.get_job_location_by_requests(job_loc)
            job_loc = job_state
            db.create({"company": f"{company_name}", "jobid": f"{job_id}", "title": f"{job_title}",
                       "retrievetime": f"{datetime.datetime.now()}", "applied": "0", "link": f"{job_link}",
                       "location": f"{job_loc}"})
